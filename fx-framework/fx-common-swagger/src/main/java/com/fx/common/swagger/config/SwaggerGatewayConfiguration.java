package com.fx.common.swagger.config;

import com.github.xiaoymin.knife4j.gateway.config.Knife4jGatewayProperties;
import com.github.xiaoymin.knife4j.gateway.filter.basic.BasicAuthFilter;
import com.github.xiaoymin.knife4j.gateway.filter.basic.BasicAuthFilterProperties;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.config.GatewayProperties;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * Swagger 网关聚合配置
 * 参考 yudao-cloud 实现，支持多服务文档聚合
 * 
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnClass({Knife4jGatewayProperties.class})
@ConditionalOnProperty(prefix = "knife4j.gateway", name = "enabled", havingValue = "true")
@EnableConfigurationProperties({Knife4jGatewayProperties.class, BasicAuthFilterProperties.class})
public class SwaggerGatewayConfiguration {

    /**
     * 配置 Knife4j 网关聚合
     */
    @Bean
    public Knife4jGatewayProperties knife4jGatewayProperties(GatewayProperties gatewayProperties) {
        Knife4jGatewayProperties properties = new Knife4jGatewayProperties();
        
        // 启用聚合功能
        properties.setEnable(true);
        properties.setStrategy("discover");
        properties.setDiscover(buildDiscoverConfig(gatewayProperties));
        
        return properties;
    }

    /**
     * 构建服务发现配置
     */
    private Knife4jGatewayProperties.Discover buildDiscoverConfig(GatewayProperties gatewayProperties) {
        Knife4jGatewayProperties.Discover discover = new Knife4jGatewayProperties.Discover();
        discover.setEnabled(true);
        discover.setVersion("openapi3");
        
        // 从网关路由配置中自动发现服务
        List<Knife4jGatewayProperties.Discover.Service> services = new ArrayList<>();
        
        if (gatewayProperties.getRoutes() != null) {
            for (RouteDefinition route : gatewayProperties.getRoutes()) {
                String routeId = route.getId();
                if (isSwaggerService(routeId)) {
                    Knife4jGatewayProperties.Discover.Service service = new Knife4jGatewayProperties.Discover.Service();
                    service.setName(getServiceDisplayName(routeId));
                    service.setUrl("/" + routeId + "/v3/api-docs");
                    service.setSwaggerVersion("3.0.3");
                    service.setServiceName(routeId);
                    services.add(service);
                }
            }
        }
        
        // 手动添加核心服务
        addCoreServices(services);
        
        discover.setServices(services);
        return discover;
    }

    /**
     * 判断是否为需要聚合文档的服务
     */
    private boolean isSwaggerService(String routeId) {
        return routeId.startsWith("fx-") && 
               !routeId.equals("fx-gateway") && 
               !routeId.contains("auth");
    }

    /**
     * 获取服务显示名称
     */
    private String getServiceDisplayName(String routeId) {
        switch (routeId) {
            case "fx-modules-system":
                return "系统管理";
            case "fx-modules-link":
                return "设备管理";
            case "fx-modules-tdengine":
                return "时序数据";
            case "fx-modules-monitor":
                return "系统监控";
            case "fx-modules-gen":
                return "代码生成";
            default:
                return routeId.replace("fx-modules-", "").replace("fx-", "");
        }
    }

    /**
     * 添加核心服务配置
     */
    private void addCoreServices(List<Knife4jGatewayProperties.Discover.Service> services) {
        // 系统管理服务
        addService(services, "fx-modules-system", "系统管理", "/system/v3/api-docs");
        
        // 设备管理服务
        addService(services, "fx-modules-link", "设备管理", "/link/v3/api-docs");
        
        // 时序数据服务
        addService(services, "fx-modules-tdengine", "时序数据", "/tdengine/v3/api-docs");
        
        // 系统监控服务
        addService(services, "fx-modules-monitor", "系统监控", "/monitor/v3/api-docs");
    }

    /**
     * 添加服务配置
     */
    private void addService(List<Knife4jGatewayProperties.Discover.Service> services, 
                           String serviceName, String displayName, String url) {
        Knife4jGatewayProperties.Discover.Service service = new Knife4jGatewayProperties.Discover.Service();
        service.setName(displayName);
        service.setUrl(url);
        service.setSwaggerVersion("3.0.3");
        service.setServiceName(serviceName);
        services.add(service);
    }

    /**
     * 基础认证过滤器（可选）
     */
    @Bean
    @ConditionalOnProperty(prefix = "knife4j.basic", name = "enable", havingValue = "true")
    public BasicAuthFilter basicAuthFilter(BasicAuthFilterProperties properties) {
        return new BasicAuthFilter(properties);
    }
}
