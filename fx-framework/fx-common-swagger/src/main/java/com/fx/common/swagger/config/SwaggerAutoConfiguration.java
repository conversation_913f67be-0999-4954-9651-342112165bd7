package com.fx.common.swagger.config;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Swagger OpenAPI 3 自动配置
 * 参考 yudao-cloud 实现，支持多模块文档聚合
 *
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnClass({OpenAPI.class})
@EnableConfigurationProperties(SwaggerProperties.class)
@ConditionalOnProperty(prefix = "swagger", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SwaggerAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public SwaggerProperties swaggerProperties() {
        return new SwaggerProperties();
    }

    /**
     * 创建 OpenAPI 配置
     */
    @Bean
    public OpenAPI customOpenAPI(SwaggerProperties swaggerProperties) {
        OpenAPI openAPI = new OpenAPI()
                .info(buildInfo(swaggerProperties))
                .addSecurityItem(new SecurityRequirement().addList("Authorization"))
                .components(new io.swagger.v3.oas.models.Components()
                        .addSecuritySchemes("Authorization",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .name("Authorization")
                                        .description("请求头中添加 Bearer Token")));

        // 设置服务器信息
        if (StrUtil.isNotBlank(swaggerProperties.getHost())) {
            Server server = new Server();
            server.setUrl(swaggerProperties.getHost());
            server.setDescription("API 服务器");
            openAPI.setServers(Collections.singletonList(server));
        }

        return openAPI;
    }

    /**
     * 创建分组 API 文档
     */
    @Bean
    public GroupedOpenApi publicApi(SwaggerProperties swaggerProperties) {
        GroupedOpenApi.Builder builder = GroupedOpenApi.builder()
                .group("default")
                .displayName("默认分组");

        // 设置扫描包路径
        if (StrUtil.isNotBlank(swaggerProperties.getBasePackage())) {
            builder.packagesToScan(swaggerProperties.getBasePackage());
        }

        // 设置路径匹配
        if (!swaggerProperties.getBasePath().isEmpty()) {
            String[] basePaths = swaggerProperties.getBasePath().toArray(new String[0]);
            builder.pathsToMatch(basePaths);
        } else {
            builder.pathsToMatch("/**");
        }

        // 排除路径
        if (!swaggerProperties.getExcludePath().isEmpty()) {
            String[] excludePaths = swaggerProperties.getExcludePath().toArray(new String[0]);
            builder.pathsToExclude(excludePaths);
        }

        return builder.build();
    }

    /**
     * 构建 API 信息
     */
    private Info buildInfo(SwaggerProperties swaggerProperties) {
        Info info = new Info()
                .title(StrUtil.isNotBlank(swaggerProperties.getTitle()) ? swaggerProperties.getTitle()
                        : "fx-links API 文档")
                .description(StrUtil.isNotBlank(swaggerProperties.getDescription()) ? swaggerProperties.getDescription()
                        : "fx-links 物联网平台 API 接口文档")
                .version(StrUtil.isNotBlank(swaggerProperties.getVersion()) ? swaggerProperties.getVersion() : "1.0");

        // 联系人信息
        if (swaggerProperties.getContact() != null) {
            SwaggerProperties.Contact contactProps = swaggerProperties.getContact();
            Contact contact = new Contact();
            if (StrUtil.isNotBlank(contactProps.getName())) {
                contact.setName(contactProps.getName());
            }
            if (StrUtil.isNotBlank(contactProps.getUrl())) {
                contact.setUrl(contactProps.getUrl());
            }
            if (StrUtil.isNotBlank(contactProps.getEmail())) {
                contact.setEmail(contactProps.getEmail());
            }
            info.setContact(contact);
        }

        // 许可证信息
        if (StrUtil.isNotBlank(swaggerProperties.getLicense())
                || StrUtil.isNotBlank(swaggerProperties.getLicenseUrl())) {
            License license = new License();
            if (StrUtil.isNotBlank(swaggerProperties.getLicense())) {
                license.setName(swaggerProperties.getLicense());
            }
            if (StrUtil.isNotBlank(swaggerProperties.getLicenseUrl())) {
                license.setUrl(swaggerProperties.getLicenseUrl());
            }
            info.setLicense(license);
        }

        return info;
    }
}
