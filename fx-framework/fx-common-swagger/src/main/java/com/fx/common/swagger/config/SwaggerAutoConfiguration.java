package com.fx.common.swagger.config;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Swagger OpenAPI 3 自动配置
 * 参考 yudao-cloud 实现，支持多模块文档聚合
 *
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnClass({OpenAPI.class})
@EnableConfigurationProperties(SwaggerProperties.class)
@ConditionalOnProperty(prefix = "swagger", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SwaggerAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public SwaggerProperties swaggerProperties() {
        return new SwaggerProperties();
    }

    /**
     * 创建 OpenAPI 配置
     * 参考 yudao-cloud 实现，支持多种认证方式
     */
    @Bean
    @Primary
    public OpenAPI customOpenAPI(SwaggerProperties swaggerProperties) {
        Map<String, SecurityScheme> securitySchemas = buildSecuritySchemes();
        OpenAPI openAPI = new OpenAPI()
                .info(buildInfo(swaggerProperties))
                .components(new io.swagger.v3.oas.models.Components().securitySchemes(securitySchemas));

        // 添加安全要求
        securitySchemas.keySet().forEach(key ->
            openAPI.addSecurityItem(new SecurityRequirement().addList(key)));

        // 设置服务器信息
        if (StrUtil.isNotBlank(swaggerProperties.getHost())) {
            Server server = new Server();
            server.setUrl(swaggerProperties.getHost());
            server.setDescription("API 服务器");
            openAPI.setServers(Collections.singletonList(server));
        }

        return openAPI;
    }

    /**
     * 构建安全认证配置
     * 支持多种认证方式：Bearer Token、API Key、Basic Auth
     */
    private Map<String, SecurityScheme> buildSecuritySchemes() {
        Map<String, SecurityScheme> securitySchemes = new HashMap<>();

        // Bearer Token 认证
        securitySchemes.put("Authorization", new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .name("Authorization")
                .description("JWT 认证，格式：Bearer {token}"));

        // API Key 认证
        securitySchemes.put("ApiKey", new SecurityScheme()
                .type(SecurityScheme.Type.APIKEY)
                .in(SecurityScheme.In.HEADER)
                .name("X-API-KEY")
                .description("API Key 认证"));

        // 租户认证
        securitySchemes.put("TenantId", new SecurityScheme()
                .type(SecurityScheme.Type.APIKEY)
                .in(SecurityScheme.In.HEADER)
                .name("tenant-id")
                .description("租户标识"));

        return securitySchemes;
    }

    /**
     * 所有模块的 API 分组
     */
    @Bean
    public GroupedOpenApi allGroupedOpenApi() {
        return buildGroupedOpenApi("all", "所有接口", "/**");
    }

    /**
     * 系统管理模块 API 分组
     */
    @Bean
    public GroupedOpenApi systemGroupedOpenApi() {
        return buildGroupedOpenApi("system", "系统管理", "/system/**");
    }

    /**
     * 设备管理模块 API 分组
     */
    @Bean
    public GroupedOpenApi linkGroupedOpenApi() {
        return buildGroupedOpenApi("link", "设备管理", "/link/**", "/deviceData/**");
    }

    /**
     * 时序数据模块 API 分组
     */
    @Bean
    public GroupedOpenApi tdengineGroupedOpenApi() {
        return buildGroupedOpenApi("tdengine", "时序数据", "/tdengine/**", "/openApi/**");
    }

    /**
     * 监控模块 API 分组
     */
    @Bean
    public GroupedOpenApi monitorGroupedOpenApi() {
        return buildGroupedOpenApi("monitor", "系统监控", "/monitor/**");
    }

    /**
     * 构建分组 API 文档
     * 参考 yudao-cloud 实现
     */
    public GroupedOpenApi buildGroupedOpenApi(String group, String displayName, String... paths) {
        return GroupedOpenApi.builder()
                .group(group)
                .displayName(displayName)
                .pathsToMatch(paths.length > 0 ? paths : new String[]{"/**"})
                .pathsToExclude("/error", "/actuator/**")
                .build();
    }

    /**
     * 构建 API 信息
     */
    private Info buildInfo(SwaggerProperties swaggerProperties) {
        Info info = new Info()
                .title(StrUtil.isNotBlank(swaggerProperties.getTitle()) ? swaggerProperties.getTitle()
                        : "fx-links API 文档")
                .description(StrUtil.isNotBlank(swaggerProperties.getDescription()) ? swaggerProperties.getDescription()
                        : "fx-links 物联网平台 API 接口文档")
                .version(StrUtil.isNotBlank(swaggerProperties.getVersion()) ? swaggerProperties.getVersion() : "1.0");

        // 联系人信息
        if (swaggerProperties.getContact() != null) {
            SwaggerProperties.Contact contactProps = swaggerProperties.getContact();
            Contact contact = new Contact();
            if (StrUtil.isNotBlank(contactProps.getName())) {
                contact.setName(contactProps.getName());
            }
            if (StrUtil.isNotBlank(contactProps.getUrl())) {
                contact.setUrl(contactProps.getUrl());
            }
            if (StrUtil.isNotBlank(contactProps.getEmail())) {
                contact.setEmail(contactProps.getEmail());
            }
            info.setContact(contact);
        }

        // 许可证信息
        if (StrUtil.isNotBlank(swaggerProperties.getLicense())
                || StrUtil.isNotBlank(swaggerProperties.getLicenseUrl())) {
            License license = new License();
            if (StrUtil.isNotBlank(swaggerProperties.getLicense())) {
                license.setName(swaggerProperties.getLicense());
            }
            if (StrUtil.isNotBlank(swaggerProperties.getLicenseUrl())) {
                license.setUrl(swaggerProperties.getLicenseUrl());
            }
            info.setLicense(license);
        }

        return info;
    }
}
