package com.fx.tdengine.controller;

import com.fx.common.core.domain.R;
import com.fx.common.core.web.controller.BaseController;
import com.fx.tdengine.service.IotDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Tag(name = "开放接口", description = "提供设备数据查询的开放API接口")
@RestController
@RequestMapping("/openApi")
@Slf4j
public class OpenApiController extends BaseController {

    @Resource
    private IotDeviceService iotDeviceService;

    @Operation(
        summary = "获取设备属性历史数据",
        description = "根据设备标识和时间范围查询设备的历史属性数据"
    )
    @Parameters({
        @Parameter(name = "deviceKey", description = "设备标识", required = true, example = "device001"),
        @Parameter(name = "beginTime", description = "开始时间", required = true, example = "2023-01-01 00:00:00"),
        @Parameter(name = "endTime", description = "结束时间", required = true, example = "2023-01-02 00:00:00"),
        @Parameter(name = "parameter", description = "查询参数，可选", required = false, example = "temperature")
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(schema = @Schema(implementation = R.class))),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "系统错误")
    })
    @GetMapping("/getDevicePropertiesHistoryData")
    public R<?> getDevicePropertiesHistoryData(
        @RequestParam("deviceKey") String deviceKey,
        @RequestParam("beginTime") String beginTime,
        @RequestParam("endTime") String endTime,
        @RequestParam(value = "parameter", required = false) String parameter) {
        List<Map<String, Object>> deviceData = iotDeviceService.getDevicePropertiesHistoryData(deviceKey, beginTime, endTime, parameter);
        return R.ok(deviceData);
    }

    @ApiOperation("获取设备属性最新数据")
    @GetMapping("/getDevicePropertiesLatestData")
    public R<?> getDevicePropertiesLatestData(@RequestParam("deviceKey") String deviceKey) {
        Map<String, Object> deviceData = iotDeviceService.getDevicePropertiesLatestData(deviceKey);
        return R.ok(deviceData);
    }
}
